# 🔍 2024_H_Car 任务调度系统分析报告

## 📋 系统架构概览

这是一个基于STM32F407的**混合式任务调度系统**，采用了**协作式调度器 + 中断驱动**的双重调度机制。

### 系统架构图

```mermaid
graph TB
    A[main函数] --> B[系统初始化]
    B --> C[Scheduler_Init]
    C --> D[System_Init]
    D --> E[各模块初始化]
    
    A --> F[主循环]
    F --> G[Scheduler_Run]
    G --> H[协作式任务调度]
    
    I[TIM2中断 1ms] --> J[HAL_TIM_PeriodElapsedCallback]
    J --> K[中断驱动任务]
    
    H --> L[Led_Task 1ms]
    H --> M[Oled_Task 10ms]
    
    K --> N[Key_Task 10ms]
    K --> O[传感器任务 5ms]
    K --> P[PID_Task 5ms]
    K --> Q[状态更新逻辑]
    
    O --> R[Encoder_Task]
    O --> S[Mpu6050_Task]
    O --> T[Gray_Task]
```

## 🏗️ 核心组件分析

### 1. 协作式调度器 (Scheduler.c)

#### 任务结构体定义

```c
// 任务结构体定义
typedef struct {
  void (*task_func)(void);  // 任务函数指针
  uint32_t rate_ms;         // 执行周期（毫秒）
  uint32_t last_run;        // 上次执行时间（初始化为 0，每次运行时刷新）
} scheduler_task_t;

// 全局变量，用于存储任务数量
uint8_t task_num;

// 静态任务数组，每个任务包含任务函数、执行周期（毫秒）和上次运行时间（毫秒）
static scheduler_task_t scheduler_task[] = {
  {Led_Task, 1, 0},      // LED任务，1ms周期
  {Oled_Task, 10, 0},    // OLED显示任务，10ms周期
  // 可根据需要启用以下任务：
  // {Gray_Task, 10, 0},    // 灰度传感器任务，10ms周期
  // {Motor_Task, 10, 0},   // 电机控制任务，10ms周期
  // {Encoder_Task, 10, 0}, // 编码器任务，10ms周期
};
```

#### 调度器核心函数

```c
/**
 * @brief 调度器初始化函数
 * 计算任务数组的元素个数，并将结果存储在 task_num 中
 */
void Scheduler_Init(void) {
  System_Init();
  // 计算任务数组的元素个数，并将结果存储在 task_num 中
  task_num = sizeof(scheduler_task) / sizeof(scheduler_task_t);
}

/**
 * @brief 调度器运行函数
 * 遍历任务数组，检查是否有任务需要执行。如果当前时间已经超过任务的执行周期，则执行该任务并更新上次运行时间
 */
void Scheduler_Run(void) {
  // 遍历任务数组中的所有任务
  for (uint8_t i = 0; i < task_num; i++) {
    // 获取当前的系统时间（毫秒）
    uint32_t now_time = HAL_GetTick();

    // 检查当前时间是否达到任务的执行时间
    if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run) {
      // 更新任务的上次运行时间为当前时间
      scheduler_task[i].last_run = now_time;

      // 执行任务函数
      scheduler_task[i].task_func();
    }
  }
}
```

**调度算法特点：**
- **时间片轮转**：基于HAL_GetTick()的毫秒级时间戳
- **非抢占式**：任务必须主动让出CPU控制权
- **周期性执行**：每个任务有固定的执行周期

### 2. 中断驱动调度器 (Scheduler_Task.c)

#### 系统初始化

```c
void System_Init(void) {
  Led_Init();
  Key_Init();
  Oled_Init();
  Uart_Init();
  Gray_Init();
  Motor_Init();
  Encoder_Init();
  Mpu6050_Init();
  PID_Init();
  Uart_Printf(&huart1, "=== System Init ===\r\n");
  HAL_TIM_Base_Start_IT(&htim2);  // 启动TIM2中断
}
```

#### 中断服务程序

```c
// TIM2 中断服务程序，1ms 中断
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim) {
  if(htim->Instance != htim2.Instance) return;
 
  /* 10ms 按键任务*/
  if(++key_timer10ms >= 10) {
    key_timer10ms = 0;
    Key_Task();
  }
  
  /* 5ms 传感器任务*/
  if(++measure_timer5ms >= 5) {
    measure_timer5ms = 0;
    Encoder_Task();    // 编码器任务
    distance += left_encoder.speed_cm_s;  // 距离累计
    Mpu6050_Task();    // 陀螺仪任务
    Gray_Task();       // 灰度传感器任务
    PID_Task();        // PID控制任务
  }
  
  /* 进圈检测逻辑 */
  if(Digtal != 0x00) {
    output_ff_flag = 1;
    if(++intput_timer500ms >= 800) intput_timer500ms = 800;
  }
  else if(output_ff_flag == 1 && intput_timer500ms == 800) {
    output_ff_flag = 0;
    intput_timer500ms = 0;
    point_count++;
    Car_State_Update();  // 状态更新
  }
  
  /* LED 状态控制 */
  if(led_state == 1 && ++led_timer500ms >= 500) {
    led_state = 0;
    led_timer500ms = 0;
  }
}
```

## ⚙️ 任务分类与优先级

| 任务类型 | 执行方式 | 周期 | 优先级 | 功能描述 |
|---------|---------|------|--------|----------|
| **高优先级中断任务** | TIM2中断 | 5ms | 最高 | 传感器数据采集、PID控制 |
| **中优先级中断任务** | TIM2中断 | 10ms | 高 | 按键处理 |
| **低优先级协作任务** | 主循环轮询 | 1ms | 中 | LED状态指示 |
| **低优先级协作任务** | 主循环轮询 | 10ms | 低 | OLED显示更新 |

### 具体任务详情

#### 高频实时任务 (5ms周期)
- **Encoder_Task()**: 编码器速度读取
- **Mpu6050_Task()**: 陀螺仪角度读取
- **Gray_Task()**: 灰度传感器循迹检测
- **PID_Task()**: PID控制算法计算

#### 中频交互任务 (10ms周期)
- **Key_Task()**: 按键状态检测和处理
- **Oled_Task()**: OLED显示内容更新

#### 高频指示任务 (1ms周期)
- **Led_Task()**: LED状态指示

## 🔄 调度策略深度分析

### 1. 双重调度机制的优势

**中断调度器负责：**
- ✅ 实时性要求高的任务（传感器读取、控制算法）
- ✅ 确保关键任务的执行时间精度
- ✅ 处理硬件事件响应
- ✅ 系统状态监控和更新

**协作式调度器负责：**
- ✅ 用户界面更新（OLED显示）
- ✅ 状态指示（LED闪烁）
- ✅ 非实时性任务
- ✅ 后台处理任务

### 2. 时间管理策略

#### 协作式调度时间管理
```c
void Scheduler_Run(void) {
  for (uint8_t i = 0; i < task_num; i++) {
    uint32_t now_time = HAL_GetTick();
    
    // 检查是否到达执行时间
    if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run) {
      scheduler_task[i].last_run = now_time;
      scheduler_task[i].task_func();  // 执行任务
    }
  }
}
```

#### 中断驱动时间管理
```c
// 使用静态计数器实现不同周期的任务调度
unsigned char measure_timer5ms;   // 5ms计数器
unsigned char key_timer10ms;      // 10ms计数器
unsigned int led_timer500ms;      // 500ms计数器
```

## 🎯 智能车特定的任务逻辑

### 1. 状态机控制系统

```c
// 每次到位置检测点时，更新系统状态同步更新小车运行状态
void Car_State_Update(void) {
  led_state = 1;
  distance = 0;
  
  switch(system_mode) {
    case 1: // 第一题：直线行驶 A -> B
      if(point_count == 1) {
        pid_running = 0;
        Motor_Brake(&left_motor);
        Motor_Brake(&right_motor);
      }
      break;
      
    case 2: // 第二题：绕行一圈 A -> B -> C -> D
      if(point_count == 1)
        pid_control_mode = 1; // 使用循迹环控制
      else if(point_count == 2) {
        pid_control_mode = 0; // 使用角度环控制
        pid_set_target(&pid_angle, -176);
      }
      else if(point_count == 3)
        pid_control_mode = 1; // 使用循迹环控制
      else if(point_count == 4) {
        pid_running = 0;
        Motor_Brake(&left_motor);
        Motor_Brake(&right_motor);
      }
      break;
      
    case 3: // 第三题：8字绕行一圈 A -> C -> B -> D
      if(point_count == 1) {
        pid_control_mode = 1; // 使用循迹环控制
      }
      else if(point_count == 2) {
        pid_control_mode = 0; // 使用角度环控制
        pid_set_target(&pid_angle, 253);
      }
      else if(point_count == 3) {
        pid_control_mode = 1; // 使用循迹环控制
      }
      else if(point_count == 4) {
        pid_running = 0;
        Motor_Brake(&left_motor);
        Motor_Brake(&right_motor);
      }
      break;
      
    case 4: // 第四题：8字绕行多圈
      if(point_count == 1) {
        pid_control_mode = 1; // 使用循迹环控制
      }
      else if(point_count == 2) {
        pid_control_mode = 0; // 使用角度环控制
        pid_set_target(&pid_angle, 253 - (0.3 * circle_count)); // 根据圈数进行微调补偿
      }
      else if(point_count == 3)
        pid_control_mode = 1; // 使用循迹环控制
      else if(point_count == 4) {
        if(++circle_count >= 4) {
          pid_running = 0;
          Motor_Brake(&left_motor);
          Motor_Brake(&right_motor);
        }
        point_count = 0;
        pid_control_mode = 0; // 使用角度环控制
        pid_set_target(&pid_angle, 0 - (0.2 * circle_count)); // 根据圈数进行微调补偿
      }
      break;
  }
  
  /* 清除历史误差 */
  pid_reset(&pid_line);
  pid_reset(&pid_angle);
}
```

### 2. 传感器融合与控制

系统实现了多传感器数据融合：

#### 灰度传感器处理
- **功能**: 8路灰度传感器循迹位置检测
- **数据**: `Digtal` 变量存储8位传感器状态
- **处理**: 加权平均算法计算位置误差

#### 编码器处理
- **功能**: 左右轮速度反馈
- **数据**: `left_encoder.speed_cm_s` 速度信息
- **应用**: 距离累计和速度控制

#### 陀螺仪处理
- **功能**: 车体角度检测
- **应用**: 角度环PID控制

#### 按键处理
- **功能**: 模式切换和参数调节
- **周期**: 10ms检测周期

## 📊 性能特征分析

### 1. 实时性能指标

| 指标 | 数值 | 说明 |
|------|------|------|
| 最高中断频率 | 1kHz | TIM2定时器中断 |
| 传感器采样率 | 200Hz | 5ms周期采样 |
| 控制环更新率 | 200Hz | PID控制频率 |
| 显示更新率 | 100Hz | OLED刷新频率 |
| 按键响应率 | 100Hz | 按键检测频率 |

### 2. 系统负载分析

**中断占用时间估算：**
- 传感器读取：~0.5ms (Encoder + Mpu6050 + Gray)
- PID计算：~0.2ms
- 按键处理：~0.1ms
- 状态更新：~0.1ms
- **总中断负载**：约15-20%

**主循环任务负载：**
- LED任务：~0.01ms
- OLED任务：~2ms (I2C通信)
- **主循环负载**：约5-10%

### 3. 内存使用分析

**静态内存分配：**
- 任务数组：`scheduler_task[]`
- 全局变量：传感器数据、控制参数
- 计数器变量：各种定时器

**优势：**
- 无动态内存分配
- 内存使用可预测
- 避免内存碎片

## 🔧 系统优化建议

### 1. 任务优先级优化

```c
// 建议的任务配置
static scheduler_task_t scheduler_task[] = {
    {Led_Task, 1, 0},          // 状态指示 - 1ms
    {Oled_Task, 20, 0},        // 显示更新 - 20ms (降低频率减少I2C占用)
    {Gray_Task, 5, 0},         // 灰度传感器 - 5ms (提高循迹精度)
    {Motor_Task, 5, 0},        // 电机控制 - 5ms (提高控制精度)
    {Encoder_Task, 10, 0},     // 编码器 - 10ms (速度反馈)
    // {Key_Task, 50, 0},      // 按键任务 - 50ms (人机交互不需要太高频率)
    // {Uart_Task, 100, 0},    // 串口任务 - 100ms (调试信息输出)
    // {Mpu6050_Task, 10, 0},  // 陀螺仪 - 10ms (角度控制)
};
```

### 2. 内存优化建议

```c
// 使用更紧凑的数据结构
typedef struct {
    void (*task_func)(void);
    uint16_t rate_ms;        // 使用16位减少内存占用
    uint16_t last_run_low;   // 只保存低16位，减少内存占用
} scheduler_task_compact_t;

// 使用位域优化标志变量
typedef struct {
    uint8_t output_ff_flag : 1;
    uint8_t intput_ff_flag : 1;
    uint8_t led_state : 1;
    uint8_t pid_running : 1;
    uint8_t pid_control_mode : 1;
    uint8_t reserved : 3;
} system_flags_t;
```

### 3. 实时性改进建议

#### 使用DMA减少CPU占用
```c
// 建议使用DMA进行I2C通信
HAL_I2C_Mem_Read_DMA(&hi2c1, device_addr, reg_addr, data_buffer, size);
```

#### 优化中断处理
```c
// 将耗时操作移出中断
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim) {
    if(htim->Instance != htim2.Instance) return;

    // 只设置标志位，具体处理在主循环中完成
    if(++measure_timer5ms >= 5) {
        measure_timer5ms = 0;
        sensor_update_flag = 1;  // 设置标志位
    }
}

// 在主循环中处理
void main_loop_tasks(void) {
    if(sensor_update_flag) {
        sensor_update_flag = 0;
        // 执行传感器更新任务
        Encoder_Task();
        Mpu6050_Task();
        Gray_Task();
        PID_Task();
    }
}
```

### 4. 调试和监控改进

```c
// 添加任务执行时间监控
typedef struct {
    uint32_t start_time;
    uint32_t max_execution_time;
    uint32_t total_execution_time;
    uint32_t execution_count;
} task_monitor_t;

// 任务性能监控
void task_performance_monitor(void) {
    static task_monitor_t monitors[MAX_TASKS];

    for(int i = 0; i < task_num; i++) {
        uint32_t start = HAL_GetTick();
        scheduler_task[i].task_func();
        uint32_t duration = HAL_GetTick() - start;

        monitors[i].execution_count++;
        monitors[i].total_execution_time += duration;
        if(duration > monitors[i].max_execution_time) {
            monitors[i].max_execution_time = duration;
        }
    }
}
```

## 🎯 总结

### 系统优势

✅ **双重调度机制**：协作式+中断驱动，保证了实时性和灵活性
✅ **模块化设计**：各任务独立，便于维护和扩展
✅ **智能车优化**：针对循迹、角度控制等应用场景优化
✅ **状态机控制**：完善的比赛模式切换逻辑
✅ **资源管理**：静态内存分配，系统稳定可靠

### 改进空间

🔄 **优先级抢占**：可以引入基于优先级的抢占式调度
🔄 **任务通信**：增加任务间数据交换和同步机制
🔄 **故障恢复**：添加任务监控和异常处理机制
🔄 **性能优化**：使用DMA、优化算法复杂度
🔄 **调试支持**：增加运行时性能监控和调试接口

### 适用场景评价

这个任务调度系统非常适合：
- 🏁 **智能车竞赛**：满足循迹、避障等实时控制需求
- 🤖 **小型机器人**：适合资源受限的嵌入式系统
- 📡 **传感器融合**：支持多传感器数据处理
- 🎮 **实时控制**：PID控制等闭环系统

总体而言，这是一个设计合理、实用性强的嵌入式任务调度系统，很好地平衡了实时性要求和系统复杂度，是智能车竞赛的优秀解决方案。
```
