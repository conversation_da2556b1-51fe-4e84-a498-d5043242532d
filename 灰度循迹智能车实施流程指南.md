# 灰度循迹智能车实施流程指南

**版权所有：米醋电子工作室**  
**文档版本：v1.0**  
**创建日期：2025-01-27**  
**项目名称：2024_H_Car 灰度循迹智能车系统**

---

## 📋 目录

1. [项目概述](#项目概述)
2. [系统架构](#系统架构)
3. [硬件配置](#硬件配置)
4. [软件架构](#软件架构)
5. [核心代码解析](#核心代码解析)
6. [调试流程](#调试流程)
7. [参数调优指南](#参数调优指南)
8. [故障排除](#故障排除)
9. [扩展功能](#扩展功能)

---

## 🎯 项目概述

### 项目背景
本项目是一个基于STM32F407VGTx微控制器的智能车循迹系统，使用8路灰度传感器进行黑线检测，通过PID控制算法实现精确循迹。

### 核心功能
- **灰度传感器循迹**：8路数字灰度传感器实时检测黑线位置
- **PID控制系统**：多环PID控制（速度环+循迹环/角度环）
- **实时显示**：OLED显示屏显示系统状态
- **任务调度**：基于时间片的多任务调度系统
- **双模式控制**：支持循迹模式和角度控制模式

---

## 🏗️ 系统架构

### 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   传感器层      │    │    控制层       │    │    执行层       │
│                 │    │                 │    │                 │
│ • 8路灰度传感器 │───▶│ • PID控制算法   │───▶│ • 双电机驱动    │
│ • MPU6050陀螺仪 │    │ • 任务调度器    │    │ • LED指示灯     │
│ • 编码器        │    │ • 状态机管理    │    │ • OLED显示      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 软件分层架构
```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (App Layer)                        │
│  gray_app.c | motor_app.c | pid_app.c | oled_app.c         │
├─────────────────────────────────────────────────────────────┤
│                   驱动层 (Driver Layer)                      │
│  motor_driver.c | encoder_driver.c | oled_driver.c         │
├─────────────────────────────────────────────────────────────┤
│                   模块层 (Module Layer)                      │
│  PID算法 | 灰度传感器 | OLED显示 | 任务调度器               │
├─────────────────────────────────────────────────────────────┤
│                   HAL层 (Hardware Layer)                    │
│  STM32F4xx HAL库 | GPIO | I2C | TIM | UART                 │
└─────────────────────────────────────────────────────────────┘
```

---

## ⚙️ 硬件配置

### 主控芯片
- **型号**：STM32F407VGTx
- **架构**：ARM Cortex-M4
- **主频**：168MHz
- **Flash**：1MB
- **RAM**：192KB

### 传感器配置
| 传感器 | 接口 | 地址/引脚 | 功能 |
|--------|------|-----------|------|
| 8路灰度传感器 | I2C3 | 0x4C | 黑线检测 |
| MPU6050陀螺仪 | I2C1 | 0x68 | 姿态检测 |
| 编码器(左) | TIM2 | PA0/PA1 | 速度反馈 |
| 编码器(右) | TIM4 | PB6/PB7 | 速度反馈 |

### 执行器配置
| 执行器 | 接口 | 引脚 | 功能 |
|--------|------|------|------|
| 左电机 | TIM1_CH1 | PE9 | PWM驱动 |
| 右电机 | TIM1_CH2 | PE11 | PWM驱动 |
| OLED显示屏 | I2C1 | PB8/PB9 | 状态显示 |
| LED指示灯 | GPIO | PC13 | 状态指示 |

---

## 💻 软件架构

### 任务调度系统
系统采用基于时间片的协作式多任务调度，主要任务包括：

```c
// 位置：../User/Scheduler.c
static scheduler_task_t scheduler_task[] = {
    {Led_Task, 1, 0},      // LED任务，1ms周期
    {Oled_Task, 10, 0},    // OLED显示任务，10ms周期
    // 可根据需要启用以下任务：
    // {Gray_Task, 10, 0},    // 灰度传感器任务，10ms周期
    // {Motor_Task, 10, 0},   // 电机控制任务，10ms周期
    // {Encoder_Task, 10, 0}, // 编码器任务，10ms周期
};
```

### 主程序流程
```c
// 位置：../Core/Src/main.c
int main(void) {
    HAL_Init();                    // HAL库初始化
    SystemClock_Config();          // 系统时钟配置
    
    // 外设初始化
    MX_GPIO_Init();
    MX_I2C1_Init();
    MX_I2C3_Init();
    MX_TIM1_Init();
    MX_TIM2_Init();
    MX_TIM3_Init();
    MX_TIM4_Init();
    
    // 应用初始化
    OLED_Init();                   // OLED显示初始化
    Scheduler_Init();              // 任务调度器初始化
    
    while(1) {
        OLED_ShowNum(1,1,110,3,12,1); // 显示状态信息
        Scheduler_Run();               // 运行任务调度器
    }
}
```

---

## 🔍 核心代码解析

### 1. 灰度传感器数据处理

#### 文件位置：`../User/App/gray_app.c`

```c
// 核心变量定义
unsigned char Digtal;                    // 8位数字传感器数据
float gray_weights[8] = {-4.0f, -3.0f, -2.0f, -1.0f, 
                         1.0f, 2.0f, 3.0f, 4.0f};  // 传感器权重
float g_line_position_error;             // 循迹位置误差

// 灰度传感器任务函数
void Gray_Task(void) {
    // 读取8路灰度传感器数据（取反处理）
    Digtal = ~IIC_Get_Digtal();
    
    float weighted_sum = 0;
    uint8_t black_line_count = 0;
    
    // 计算加权位置
    for(uint8_t i = 0; i < 8; i++) {
        if((Digtal>>i) & 0x01) {        // 检测到黑线
            weighted_sum += gray_weights[i];
            black_line_count++;
        }
    }
    
    // 计算位置误差
    if(black_line_count > 0)
        g_line_position_error = weighted_sum / (float)black_line_count;
}
```

**关键变量说明：**
- `Digtal`：8位数据，每位代表一个传感器状态（1=检测到黑线，0=白色）
- `gray_weights[]`：传感器位置权重，中间为0，两侧权重递增
- `g_line_position_error`：计算得出的位置偏差，用于PID控制

### 2. PID控制系统

#### 文件位置：`../User/App/pid_app.c`

```c
// PID控制器实例
PID_T pid_speed_left;   // 左轮速度环
PID_T pid_speed_right;  // 右轮速度环
PID_T pid_line;         // 循迹环
PID_T pid_angle;        // 角度环

// 循迹PID参数（重要调试参数）
PidParams_t pid_params_line = {
    .kp = 10.0f,        // 比例系数：响应速度
    .ki = 0.0000f,      // 积分系数：消除稳态误差
    .kd = 160.00f,      // 微分系数：抑制震荡
    .out_min = -999.0f,
    .out_max = 999.0f,
};

// 循迹控制函数
void Line_PID_control(void) {
    int line_pid_output = 0;
    
    // 使用位置式PID计算输出
    line_pid_output = pid_calculate_positional(&pid_line, g_line_position_error);
    
    // 输出限幅
    line_pid_output = pid_constrain(line_pid_output, 
                                   pid_params_line.out_min, 
                                   pid_params_line.out_max);
    
    // 差速控制：左右轮速度差值
    pid_set_target(&pid_speed_left, basic_speed - line_pid_output);
    pid_set_target(&pid_speed_right, basic_speed + line_pid_output);
}
```

**控制逻辑说明：**
1. **双环控制**：外环（循迹环）+ 内环（速度环）
2. **差速转向**：通过左右轮速度差实现转向
3. **参数含义**：
   - `kp`：比例系数，影响响应速度
   - `ki`：积分系数，消除稳态误差
   - `kd`：微分系数，抑制超调和震荡

### 3. 电机控制系统

#### 文件位置：`../User/Driver/motor_driver.c`

```c
// 电机结构体定义
typedef struct {
    TIM_HandleTypeDef* htim;    // PWM定时器
    uint32_t channel;           // PWM通道
    GPIO_TypeDef* in1_port;     // 方向控制引脚1
    uint16_t in1_pin;
    GPIO_TypeDef* in2_port;     // 方向控制引脚2
    uint16_t in2_pin;
    int16_t speed;              // 当前速度
    uint16_t max_speed;         // 最大速度限制
} MOTOR;

// 电机速度设置函数
void Motor_Set_Speed(MOTOR* motor, int16_t speed) {
    motor->speed = speed;
    
    if(speed > 0) {
        // 正转
        HAL_GPIO_WritePin(motor->in1_port, motor->in1_pin, GPIO_PIN_SET);
        HAL_GPIO_WritePin(motor->in2_port, motor->in2_pin, GPIO_PIN_RESET);
        __HAL_TIM_SET_COMPARE(motor->htim, motor->channel, abs(speed));
    } else if(speed < 0) {
        // 反转
        HAL_GPIO_WritePin(motor->in1_port, motor->in1_pin, GPIO_PIN_RESET);
        HAL_GPIO_WritePin(motor->in2_port, motor->in2_pin, GPIO_PIN_SET);
        __HAL_TIM_SET_COMPARE(motor->htim, motor->channel, abs(speed));
    } else {
        // 停止
        HAL_GPIO_WritePin(motor->in1_port, motor->in1_pin, GPIO_PIN_RESET);
        HAL_GPIO_WritePin(motor->in2_port, motor->in2_pin, GPIO_PIN_RESET);
        __HAL_TIM_SET_COMPARE(motor->htim, motor->channel, 0);
    }
}
```

---

## 🔧 调试流程

### 第一步：硬件连接检查

1. **电源检查**
   - 确认主控板供电正常（5V/3.3V）
   - 检查电机驱动板供电（通常7.4V锂电池）
   - 测量各传感器供电电压

2. **通信接口检查**
   ```c
   // 在main.c中添加测试代码
   if(Ping() == 0) {
       // 灰度传感器通信正常
       OLED_ShowString(1, 1, "Gray OK", 12, 1);
   } else {
       // 通信异常
       OLED_ShowString(1, 1, "Gray ERR", 12, 1);
   }
   ```

3. **电机测试**
   ```c
   // 在Motor_Task中添加测试代码
   void Motor_Task(void) {
       static uint32_t test_time = 0;
       if(HAL_GetTick() - test_time > 2000) {
           test_time = HAL_GetTick();
           // 左电机正转2秒
           Motor_Set_Speed(&left_motor, 100);
           Motor_Set_Speed(&right_motor, 100);
       }
   }
   ```

### 第二步：传感器数据验证

1. **启用灰度传感器任务**
   ```c
   // 在Scheduler.c中取消注释
   static scheduler_task_t scheduler_task[] = {
       {Led_Task, 1, 0},
       {Oled_Task, 10, 0},
       {Gray_Task, 10, 0},    // 启用灰度传感器任务
   };
   ```

2. **显示传感器数据**
   ```c
   // 在gray_app.c中添加调试代码
   void Gray_Task(void) {
       Digtal = ~IIC_Get_Digtal();
       
       // 显示8位传感器状态
       char debug_str[20];
       sprintf(debug_str, "%d%d%d%d%d%d%d%d", 
               (Digtal>>0)&0x01, (Digtal>>1)&0x01, (Digtal>>2)&0x01, (Digtal>>3)&0x01,
               (Digtal>>4)&0x01, (Digtal>>5)&0x01, (Digtal>>6)&0x01, (Digtal>>7)&0x01);
       OLED_ShowString(2, 1, debug_str, 12, 1);
       
       // 计算位置误差并显示
       // ... 位置计算代码 ...
       
       char pos_str[10];
       sprintf(pos_str, "Pos:%.2f", g_line_position_error);
       OLED_ShowString(3, 1, pos_str, 12, 1);
   }
   ```

### 第三步：PID参数调试

1. **启用PID控制**
   ```c
   // 在Scheduler.c中启用所有相关任务
   static scheduler_task_t scheduler_task[] = {
       {Led_Task, 1, 0},
       {Oled_Task, 10, 0},
       {Gray_Task, 10, 0},
       {Motor_Task, 10, 0},
       {Encoder_Task, 10, 0},
   };
   ```

2. **在Motor_Task中添加PID控制**
   ```c
   // 在motor_app.c中修改Motor_Task
   void Motor_Task(void) {
       PID_Task();  // 调用PID控制任务
   }
   ```

3. **启用PID运行标志**
   ```c
   // 在系统初始化后设置
   void System_Init(void) {
       // ... 其他初始化代码 ...
       PID_Init();
       pid_running = true;           // 启用PID控制
       pid_control_mode = 1;         // 设置为循迹模式
   }
   ```

---

## ⚡ 参数调优指南

### PID参数调试方法

#### 1. 循迹PID参数调优（最重要）

**位置：** `../User/App/pid_app.c` 中的 `pid_params_line`

```c
PidParams_t pid_params_line = {
    .kp = 10.0f,        // 【重点调试】比例系数
    .ki = 0.0000f,      // 积分系数（建议保持0）
    .kd = 160.00f,      // 【重点调试】微分系数
    .out_min = -999.0f,
    .out_max = 999.0f,
};
```

**调试步骤：**

1. **第一步：调整Kp（比例系数）**
   - 初始值：`kp = 5.0f`
   - 现象观察：
     - 太小：反应迟钝，转弯不够
     - 太大：震荡严重，左右摆动
   - 调试方法：逐步增加，直到出现轻微震荡

2. **第二步：调整Kd（微分系数）**
   - 初始值：`kd = 50.0f`
   - 现象观察：
     - 太小：超调严重，过弯后摆动
     - 太大：反应过于敏感，抖动
   - 调试方法：在Kp基础上增加Kd，抑制震荡

3. **第三步：微调基础速度**
   ```c
   int basic_speed = 94;  // 【可调参数】基础速度
   ```
   - 速度过快：容易冲出赛道
   - 速度过慢：循迹精度高但效率低

#### 2. 速度环PID参数

```c
PidParams_t pid_params_left = {
    .kp = 15.0f,        // 速度响应系数
    .ki = 0.1000f,      // 消除速度稳态误差
    .kd = 0.00f,        // 速度环一般不需要微分
};
```

**调试要点：**
- 速度环主要保证电机响应速度
- Ki值不宜过大，避免积分饱和
- 左右轮参数应保持一致

### 传感器参数调优

#### 1. 灰度传感器权重调整

```c
// 位置：../User/App/gray_app.c
float gray_weights[8] = {-4.0f, -3.0f, -2.0f, -1.0f, 
                         1.0f, 2.0f, 3.0f, 4.0f};
```

**调整原则：**
- 中间传感器权重小，边缘传感器权重大
- 权重差值影响转向灵敏度
- 可根据传感器安装位置微调

#### 2. 传感器阈值调整

如需要模拟量处理，可在硬件层调整：
```c
// 在hardware_iic.c中添加阈值处理
unsigned char IIC_Get_Digtal_With_Threshold(uint8_t threshold) {
    unsigned char analog_data[8];
    unsigned char digital_result = 0;
    
    IIC_Get_Anolog(analog_data, 8);
    
    for(int i = 0; i < 8; i++) {
        if(analog_data[i] < threshold) {  // 黑线检测阈值
            digital_result |= (1 << i);
        }
    }
    return digital_result;
}
```

---

## 🚨 故障排除

### 常见问题及解决方案

#### 1. 小车不动或动作异常

**可能原因：**
- PID控制未启用
- 电机驱动异常
- 电源供电不足

**排查步骤：**
```c
// 1. 检查PID使能状态
if(pid_running == false) {
    OLED_ShowString(4, 1, "PID OFF", 12, 1);
}

// 2. 检查电机输出
char motor_debug[20];
sprintf(motor_debug, "L:%d R:%d", left_motor.speed, right_motor.speed);
OLED_ShowString(4, 1, motor_debug, 12, 1);

// 3. 强制电机测试
Motor_Set_Speed(&left_motor, 100);
Motor_Set_Speed(&right_motor, 100);
```

#### 2. 循迹效果差

**可能原因：**
- 传感器数据异常
- PID参数不合适
- 机械安装问题

**排查步骤：**
```c
// 1. 检查传感器数据
void Gray_Debug_Display(void) {
    char sensor_str[10];
    sprintf(sensor_str, "%02X", Digtal);
    OLED_ShowString(1, 1, sensor_str, 12, 1);
    
    char pos_str[15];
    sprintf(pos_str, "Err:%.2f", g_line_position_error);
    OLED_ShowString(2, 1, pos_str, 12, 1);
}

// 2. 显示PID输出
void PID_Debug_Display(void) {
    char pid_str[15];
    sprintf(pid_str, "Out:%d", (int)pid_line.out);
    OLED_ShowString(3, 1, pid_str, 12, 1);
}
```

#### 3. 系统死机或重启

**可能原因：**
- 栈溢出
- 硬件故障中断
- 看门狗复位

**排查方法：**
```c
// 在main.c中添加错误处理
void Error_Handler(void) {
    __disable_irq();
    OLED_ShowString(1, 1, "ERROR!", 12, 1);
    while (1) {
        HAL_GPIO_TogglePin(GPIOC, GPIO_PIN_13);  // LED闪烁指示错误
        HAL_Delay(200);
    }
}
```

### 调试工具使用

#### 1. 串口调试

```c
// 在uart_app.c中添加调试输出
void Debug_Print_Status(void) {
    printf("Sensor: %02X, Error: %.2f, PID: %.2f\r\n", 
           Digtal, g_line_position_error, pid_line.out);
}
```

#### 2. OLED实时显示

```c
// 在oled_app.c中添加状态显示
void Oled_Task(void) {
    OLED_ShowString(1, 1, "2024_H_Car", 12, 1);
    
    char status_str[20];
    sprintf(status_str, "Mode:%s", pid_control_mode ? "Line" : "Angle");
    OLED_ShowString(2, 1, status_str, 12, 1);
    
    sprintf(status_str, "Speed:%d", basic_speed);
    OLED_ShowString(3, 1, status_str, 12, 1);
    
    sprintf(status_str, "Err:%.2f", g_line_position_error);
    OLED_ShowString(4, 1, status_str, 12, 1);
}
```

---

## 🔄 任务逻辑修改指南

### 修改任务调度

**文件位置：** `../User/Scheduler.c`

```c
// 启用/禁用任务的方法
static scheduler_task_t scheduler_task[] = {
    {Led_Task, 1, 0},          // LED任务 - 保持启用
    {Oled_Task, 10, 0},        // OLED显示 - 保持启用
    {Gray_Task, 10, 0},        // 灰度传感器 - 循迹必需
    {Motor_Task, 10, 0},       // 电机控制 - 循迹必需
    {Encoder_Task, 10, 0},     // 编码器 - 速度反馈必需
    // {Key_Task, 10, 0},      // 按键任务 - 可选
    // {Uart_Task, 10, 0},     // 串口任务 - 调试用
    // {Mpu6050_Task, 1, 0},   // 陀螺仪 - 角度控制用
};
```

**修改任务周期：**
- 高频任务（1-5ms）：电机控制、传感器读取
- 中频任务（10-20ms）：PID计算、显示更新
- 低频任务（50-100ms）：状态监控、通信

### 添加自定义任务

1. **创建任务函数**
   ```c
   // 在对应的app文件中添加
   void Custom_Task(void) {
       // 自定义任务逻辑
       static uint32_t counter = 0;
       counter++;
       
       // 每100次执行一次特殊操作
       if(counter % 100 == 0) {
           // 特殊操作代码
       }
   }
   ```

2. **注册任务到调度器**
   ```c
   // 在Scheduler.c中添加
   {Custom_Task, 50, 0},  // 50ms周期执行
   ```

3. **在头文件中声明**
   ```c
   // 在对应的.h文件中添加
   void Custom_Task(void);
   ```

### 修改控制逻辑

#### 1. 切换控制模式

```c
// 在pid_app.c中修改控制模式切换逻辑
void Switch_Control_Mode(void) {
    static uint32_t mode_switch_time = 0;
    
    // 每10秒切换一次模式（示例）
    if(HAL_GetTick() - mode_switch_time > 10000) {
        mode_switch_time = HAL_GetTick();
        pid_control_mode = !pid_control_mode;  // 切换模式
        
        // 重置PID状态
        pid_reset(&pid_line);
        pid_reset(&pid_angle);
    }
}
```

#### 2. 添加速度控制逻辑

```c
// 在pid_app.c中添加动态速度调整
void Dynamic_Speed_Control(void) {
    // 根据弯道程度调整速度
    float abs_error = fabs(g_line_position_error);
    
    if(abs_error > 2.0f) {
        basic_speed = 60;   // 急弯减速
    } else if(abs_error > 1.0f) {
        basic_speed = 80;   // 缓弯减速
    } else {
        basic_speed = 100;  // 直道加速
    }
}
```

#### 3. 添加安全保护逻辑

```c
// 在gray_app.c中添加脱轨检测
void Track_Lost_Detection(void) {
    static uint32_t lost_time = 0;
    
    if(black_line_count == 0) {  // 没有检测到黑线
        if(lost_time == 0) {
            lost_time = HAL_GetTick();
        } else if(HAL_GetTick() - lost_time > 1000) {  // 脱轨超过1秒
            // 紧急停车
            pid_running = false;
            Motor_Set_Speed(&left_motor, 0);
            Motor_Set_Speed(&right_motor, 0);
            OLED_ShowString(4, 1, "TRACK LOST!", 12, 1);
        }
    } else {
        lost_time = 0;  // 重置脱轨计时
    }
}
```

---

## 📈 扩展功能

### 1. 添加蓝牙控制

```c
// 在uart_app.c中添加蓝牙命令处理
void Bluetooth_Command_Process(char* cmd) {
    if(strcmp(cmd, "START") == 0) {
        pid_running = true;
    } else if(strcmp(cmd, "STOP") == 0) {
        pid_running = false;
    } else if(strncmp(cmd, "SPEED", 5) == 0) {
        int speed = atoi(cmd + 5);
        basic_speed = speed;
    } else if(strncmp(cmd, "KP", 2) == 0) {
        float kp = atof(cmd + 2);
        pid_set_params(&pid_line, kp, pid_params_line.ki, pid_params_line.kd);
    }
}
```

### 2. 添加数据记录功能

```c
// 在新建的data_log.c中添加
typedef struct {
    uint32_t timestamp;
    float position_error;
    float pid_output;
    int16_t left_speed;
    int16_t right_speed;
} DataLog_t;

DataLog_t data_buffer[1000];
uint16_t log_index = 0;

void Data_Log_Record(void) {
    if(log_index < 1000) {
        data_buffer[log_index].timestamp = HAL_GetTick();
        data_buffer[log_index].position_error = g_line_position_error;
        data_buffer[log_index].pid_output = pid_line.out;
        data_buffer[log_index].left_speed = left_motor.speed;
        data_buffer[log_index].right_speed = right_motor.speed;
        log_index++;
    }
}
```

### 3. 添加自适应PID

```c
// 在pid_app.c中添加自适应PID算法
void Adaptive_PID_Tuning(void) {
    static float error_history[10] = {0};
    static uint8_t history_index = 0;
    
    // 记录误差历史
    error_history[history_index] = g_line_position_error;
    history_index = (history_index + 1) % 10;
    
    // 计算误差方差
    float variance = 0;
    float mean = 0;
    for(int i = 0; i < 10; i++) {
        mean += error_history[i];
    }
    mean /= 10;
    
    for(int i = 0; i < 10; i++) {
        variance += (error_history[i] - mean) * (error_history[i] - mean);
    }
    variance /= 10;
    
    // 根据方差调整PID参数
    if(variance > 1.0f) {  // 震荡严重
        pid_params_line.kd += 5.0f;  // 增加微分
        pid_params_line.kp -= 0.5f;  // 减少比例
    } else if(variance < 0.1f) {  // 响应迟钝
        pid_params_line.kp += 0.5f;  // 增加比例
    }
    
    // 更新PID参数
    pid_set_params(&pid_line, pid_params_line.kp, 
                   pid_params_line.ki, pid_params_line.kd);
}
```

---

## 📝 总结

本文档详细介绍了灰度循迹智能车的完整实施流程，包括：

1. **系统架构理解**：掌握硬件配置和软件分层
2. **核心代码解析**：理解关键算法和数据流
3. **调试流程指导**：从硬件检查到参数调优
4. **故障排除方法**：常见问题的诊断和解决
5. **功能扩展指南**：如何添加新功能和优化性能

**重要提醒：**
- 调试时务必确保安全，避免小车冲出工作区域
- PID参数调试需要耐心，建议小步调整
- 保持代码备份，避免调试过程中丢失工作成果
- 充分利用OLED显示和串口输出进行调试

**版权声明：** 本文档及相关代码版权归米醋电子工作室所有。

---

*文档结束*
